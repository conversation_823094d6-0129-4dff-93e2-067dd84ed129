{"version": 3, "sources": ["src/pages/team-management/components/TeamMemberManagement.tsx", "src/pages/team-management/components/TeamSettings.tsx", "src/pages/team-management/index.tsx"], "sourcesContent": ["/**\n * 团队成员管理组件\n * \n * 功能特性：\n * - 查看团队成员列表及详细信息\n * - 添加新成员（通过邮箱邀请）\n * - 移除团队现有成员\n * - 批量操作支持\n * - 成员搜索和筛选\n * \n * 权限控制：\n * - 只有团队创建者可以进行成员管理操作\n * - 创建者不能移除自己\n * - 提供详细的操作确认\n */\n\nimport React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Modal,\n  Form,\n  message,\n  Tag,\n  Avatar,\n  Typography,\n  Popconfirm,\n  Select,\n  Divider,\n  Row,\n  Col,\n  Statistic\n} from 'antd';\nimport {\n  UserAddOutlined,\n  DeleteOutlined,\n  SearchOutlined,\n  MailOutlined,\n  UserOutlined,\n  CrownOutlined,\n  TeamOutlined\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse, TeamMemberResponse } from '@/types/api';\n\nconst { Text, Title } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamMemberManagementProps {\n  teamDetail: TeamDetailResponse;\n  onRefresh: () => void;\n}\n\nconst TeamMemberManagement: React.FC<TeamMemberManagementProps> = ({\n  teamDetail,\n  onRefresh\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [members, setMembers] = useState<TeamMemberResponse[]>([]);\n  const [searchText, setSearchText] = useState('');\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\n  const [inviteModalVisible, setInviteModalVisible] = useState(false);\n  const [inviteForm] = Form.useForm();\n\n  useEffect(() => {\n    fetchMembers();\n  }, []);\n\n  const fetchMembers = async () => {\n    try {\n      setLoading(true);\n      const memberList = await TeamService.getCurrentTeamMembers();\n      setMembers(memberList || []);\n    } catch (error) {\n      console.error('获取团队成员失败:', error);\n      message.error('获取团队成员失败');\n      setMembers([]); // 确保在错误时设置为空数组\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 邀请新成员\n  const handleInviteMembers = async (values: { emails: string }) => {\n    try {\n      const emailList = values.emails\n        .split('\\n')\n        .map(email => email.trim())\n        .filter(email => email);\n\n      await TeamService.inviteMembers({ emails: emailList });\n      message.success(`成功邀请 ${emailList.length} 名成员`);\n      setInviteModalVisible(false);\n      inviteForm.resetFields();\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('邀请成员失败:', error);\n      message.error('邀请成员失败');\n    }\n  };\n\n  // 移除单个成员\n  const handleRemoveMember = async (member: TeamMemberResponse) => {\n    try {\n      await TeamService.removeMember(member.id);\n      message.success(`已移除成员：${member.name}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('移除成员失败:', error);\n      message.error('移除成员失败');\n    }\n  };\n\n  // 批量移除成员\n  const handleBatchRemove = async () => {\n    try {\n      const memberIds = selectedRowKeys as number[];\n      for (const memberId of memberIds) {\n        await TeamService.removeMember(memberId);\n      }\n      message.success(`已移除 ${memberIds.length} 名成员`);\n      setSelectedRowKeys([]);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('批量移除成员失败:', error);\n      message.error('批量移除成员失败');\n    }\n  };\n\n  // 筛选成员\n  const filteredMembers = (members || []).filter(member =>\n    member.name.toLowerCase().includes(searchText.toLowerCase()) ||\n    member.email.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // 停用/启用成员\n  const handleToggleMemberStatus = async (member: TeamMemberResponse, isActive: boolean) => {\n    try {\n      await TeamService.updateMemberStatus(member.id, isActive);\n      message.success(`已${isActive ? '启用' : '停用'}成员：${member.name}`);\n      fetchMembers();\n      onRefresh();\n    } catch (error) {\n      console.error('更新成员状态失败:', error);\n      message.error('更新成员状态失败');\n    }\n  };\n\n  // 表格列配置\n  const columns: ColumnsType<TeamMemberResponse> = [\n    {\n      title: '姓名',\n      dataIndex: 'name',\n      key: 'name',\n      render: (name: string, record) => (\n        <Space>\n          <Text strong>{name}</Text>\n          {record.isCreator && (\n            <Tag icon={<CrownOutlined />} color=\"gold\">创建者</Tag>\n          )}\n        </Space>\n      ),\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n      render: (email: string) => (\n        <Text type=\"secondary\">{email}</Text>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'isActive',\n      key: 'status',\n      width: 100,\n      render: (isActive: boolean) => (\n        <Tag color={isActive ? 'green' : 'red'}>\n          {isActive ? '启用' : '停用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '加入时间',\n      dataIndex: 'assignedAt',\n      key: 'assignedAt',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '最后访问',\n      dataIndex: 'lastAccessTime',\n      key: 'lastAccessTime',\n      width: 150,\n      render: (date: string) => new Date(date).toLocaleDateString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_, record) => {\n        if (record.isCreator) {\n          return <Text type=\"secondary\">-</Text>;\n        }\n\n        return (\n          <Space>\n            {record.isActive ? (\n              <Button\n                type=\"text\"\n                size=\"small\"\n                onClick={() => handleToggleMemberStatus(record, false)}\n              >\n                停用\n              </Button>\n            ) : (\n              <Button\n                type=\"text\"\n                size=\"small\"\n                onClick={() => handleToggleMemberStatus(record, true)}\n              >\n                启用\n              </Button>\n            )}\n            <Popconfirm\n              title=\"确认移除成员\"\n              description={`确定要移除成员 ${record.name} 吗？此操作不可恢复。`}\n              onConfirm={() => handleRemoveMember(record)}\n              okText=\"确认\"\n              cancelText=\"取消\"\n              okType=\"danger\"\n            >\n              <Button\n                type=\"text\"\n                danger\n                size=\"small\"\n                icon={<DeleteOutlined />}\n              >\n                移除\n              </Button>\n            </Popconfirm>\n          </Space>\n        );\n      },\n    },\n  ];\n\n  // 行选择配置\n  const rowSelection = {\n    selectedRowKeys,\n    onChange: setSelectedRowKeys,\n    getCheckboxProps: (record: TeamMemberResponse) => ({\n      disabled: record.isCreator, // 创建者不能被选择\n    }),\n  };\n\n  return (\n    <div>\n      {/* 统计信息 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"团队成员总数\"\n              value={(members || []).length}\n              prefix={<TeamOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"活跃成员\"\n              value={(members || []).filter(m => m.isActive).length}\n              prefix={<UserOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"待激活成员\"\n              value={(members || []).filter(m => !m.isActive).length}\n              prefix={<MailOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"管理员\"\n              value={(members || []).filter(m => m.isCreator).length}\n              prefix={<CrownOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 操作栏 */}\n      <Card style={{ marginBottom: 16 }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Input\n                placeholder=\"搜索成员姓名或邮箱\"\n                prefix={<SearchOutlined />}\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                style={{ width: 300 }}\n                allowClear\n              />\n              {selectedRowKeys.length > 0 && (\n                <Popconfirm\n                  title=\"批量移除成员\"\n                  description={`确定要移除选中的 ${selectedRowKeys.length} 名成员吗？此操作不可恢复。`}\n                  onConfirm={handleBatchRemove}\n                  okText=\"确认\"\n                  cancelText=\"取消\"\n                  okType=\"danger\"\n                >\n                  <Button\n                    danger\n                    icon={<DeleteOutlined />}\n                  >\n                    批量移除 ({selectedRowKeys.length})\n                  </Button>\n                </Popconfirm>\n              )}\n            </Space>\n          </Col>\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<UserAddOutlined />}\n              onClick={() => setInviteModalVisible(true)}\n            >\n              邀请成员\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 成员列表 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={filteredMembers}\n          rowKey=\"id\"\n          loading={loading}\n          rowSelection={rowSelection}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 名成员`,\n            pageSize: 10,\n          }}\n        />\n      </Card>\n\n      {/* 邀请成员弹窗 */}\n      <Modal\n        title=\"邀请新成员\"\n        open={inviteModalVisible}\n        onCancel={() => {\n          setInviteModalVisible(false);\n          inviteForm.resetFields();\n        }}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={inviteForm}\n          layout=\"vertical\"\n          onFinish={handleInviteMembers}\n        >\n          <Form.Item\n            name=\"emails\"\n            label=\"邮箱地址\"\n            rules={[\n              { required: true, message: '请输入邮箱地址' },\n            ]}\n            extra=\"每行一个邮箱地址，支持批量邀请\"\n          >\n            <TextArea\n              rows={6}\n              placeholder=\"请输入邮箱地址，每行一个&#10;例如：&#10;<EMAIL>&#10;<EMAIL>\"\n            />\n          </Form.Item>\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\" icon={<MailOutlined />}>\n                发送邀请\n              </Button>\n              <Button onClick={() => setInviteModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default TeamMemberManagement;\n", "/**\n * 团队设置组件\n * \n * 功能特性：\n * - 编辑/修改团队名称和描述\n * - 删除整个团队\n * - 团队基本信息管理\n * - 危险操作确认\n * \n * 权限控制：\n * - 只有团队创建者可以进行设置操作\n * - 删除团队需要二次确认\n */\n\nimport React, { useState } from 'react';\nimport {\n  Card,\n  Form,\n  Input,\n  Button,\n  Space,\n  Typography,\n  Divider,\n  Modal,\n  Alert,\n  Row,\n  Col,\n  Statistic,\n  message,\n  Popconfirm\n} from 'antd';\nimport {\n  EditOutlined,\n  DeleteOutlined,\n  SaveOutlined,\n  TeamOutlined,\n  CalendarOutlined,\n  UserOutlined,\n  ExclamationCircleOutlined,\n  WarningOutlined\n} from '@ant-design/icons';\nimport { history } from '@umijs/max';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse, UpdateTeamRequest } from '@/types/api';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\n\ninterface TeamSettingsProps {\n  teamDetail: TeamDetailResponse;\n  onRefresh: () => void;\n}\n\nconst TeamSettings: React.FC<TeamSettingsProps> = ({\n  teamDetail,\n  onRefresh\n}) => {\n  const [editMode, setEditMode] = useState(false);\n  const [updating, setUpdating] = useState(false);\n  const [deleteModalVisible, setDeleteModalVisible] = useState(false);\n  const [deleteConfirmText, setDeleteConfirmText] = useState('');\n  const [deleting, setDeleting] = useState(false);\n  const [form] = Form.useForm();\n\n  // 进入编辑模式\n  const handleEdit = () => {\n    form.setFieldsValue({\n      name: teamDetail.name,\n      description: teamDetail.description || '',\n    });\n    setEditMode(true);\n  };\n\n  // 取消编辑\n  const handleCancelEdit = () => {\n    setEditMode(false);\n    form.resetFields();\n  };\n\n  // 保存团队信息\n  const handleSaveTeam = async (values: UpdateTeamRequest) => {\n    try {\n      setUpdating(true);\n      await TeamService.updateCurrentTeam(values);\n      message.success('团队信息更新成功');\n      setEditMode(false);\n      onRefresh();\n    } catch (error) {\n      console.error('更新团队失败:', error);\n      message.error('更新团队失败');\n    } finally {\n      setUpdating(false);\n    }\n  };\n\n  // 删除团队\n  const handleDeleteTeam = async () => {\n    if (deleteConfirmText !== teamDetail.name) {\n      message.error('请输入正确的团队名称');\n      return;\n    }\n\n    try {\n      setDeleting(true);\n      await TeamService.deleteCurrentTeam();\n      message.success('团队已删除');\n      setDeleteModalVisible(false);\n      // 删除成功后跳转到团队选择页面\n      history.push('/user/team-select');\n    } catch (error) {\n      console.error('删除团队失败:', error);\n      message.error('删除团队失败');\n    } finally {\n      setDeleting(false);\n    }\n  };\n\n  return (\n    <div>\n      {/* 团队基本信息 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"团队ID\"\n              value={teamDetail.id}\n              prefix={<TeamOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"成员数量\"\n              value={teamDetail.memberCount}\n              prefix={<UserOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"创建时间\"\n              value={new Date(teamDetail.createdAt).toLocaleDateString()}\n              prefix={<CalendarOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"最后更新\"\n              value={new Date(teamDetail.updatedAt).toLocaleDateString()}\n              prefix={<EditOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 团队信息编辑 */}\n      <Card \n        title=\"团队信息\"\n        extra={\n          !editMode && (\n            <Button \n              type=\"primary\" \n              icon={<EditOutlined />}\n              onClick={handleEdit}\n            >\n              编辑\n            </Button>\n          )\n        }\n        style={{ marginBottom: 24 }}\n      >\n        {editMode ? (\n          <Form\n            form={form}\n            layout=\"vertical\"\n            onFinish={handleSaveTeam}\n          >\n            <Form.Item\n              name=\"name\"\n              label=\"团队名称\"\n              rules={[\n                { required: true, message: '请输入团队名称' },\n                { min: 2, max: 50, message: '团队名称长度应在2-50个字符之间' },\n              ]}\n            >\n              <Input placeholder=\"请输入团队名称\" />\n            </Form.Item>\n            <Form.Item\n              name=\"description\"\n              label=\"团队描述\"\n              rules={[\n                { max: 200, message: '团队描述不能超过200个字符' },\n              ]}\n            >\n              <TextArea \n                rows={4} \n                placeholder=\"请输入团队描述（可选）\"\n                showCount\n                maxLength={200}\n              />\n            </Form.Item>\n            <Form.Item>\n              <Space>\n                <Button \n                  type=\"primary\" \n                  htmlType=\"submit\" \n                  loading={updating}\n                  icon={<SaveOutlined />}\n                >\n                  保存\n                </Button>\n                <Button onClick={handleCancelEdit}>\n                  取消\n                </Button>\n              </Space>\n            </Form.Item>\n          </Form>\n        ) : (\n          <div>\n            <div style={{ marginBottom: 16 }}>\n              <Text strong>团队名称：</Text>\n              <Title level={4} style={{ margin: '8px 0' }}>{teamDetail.name}</Title>\n            </div>\n            <div>\n              <Text strong>团队描述：</Text>\n              <Paragraph style={{ marginTop: 8 }}>\n                {teamDetail.description || '暂无描述'}\n              </Paragraph>\n            </div>\n          </div>\n        )}\n      </Card>\n\n      {/* 危险操作区域 */}\n      <Card \n        title={\n          <Space>\n            <WarningOutlined style={{ color: '#ff4d4f' }} />\n            <Text type=\"danger\">危险操作</Text>\n          </Space>\n        }\n      >\n        <Alert\n          message=\"删除团队\"\n          description=\"删除团队将永久移除所有团队数据，包括成员关系、设置等。此操作不可恢复，请谨慎操作。\"\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: 16 }}\n        />\n        \n        <Popconfirm\n          title=\"确认删除团队\"\n          description=\"您确定要删除这个团队吗？此操作不可恢复。\"\n          onConfirm={() => setDeleteModalVisible(true)}\n          okText=\"确认\"\n          cancelText=\"取消\"\n          okType=\"danger\"\n        >\n          <Button \n            danger \n            icon={<DeleteOutlined />}\n            size=\"large\"\n          >\n            删除团队\n          </Button>\n        </Popconfirm>\n      </Card>\n\n      {/* 删除确认弹窗 */}\n      <Modal\n        title={\n          <Space>\n            <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />\n            <Text type=\"danger\">删除团队确认</Text>\n          </Space>\n        }\n        open={deleteModalVisible}\n        onCancel={() => {\n          setDeleteModalVisible(false);\n          setDeleteConfirmText('');\n        }}\n        footer={null}\n        width={600}\n      >\n        <Alert\n          message=\"警告：此操作不可恢复\"\n          description={\n            <div>\n              <p>删除团队将会：</p>\n              <ul>\n                <li>永久删除团队及所有相关数据</li>\n                <li>移除所有团队成员</li>\n                <li>清除团队设置和配置</li>\n                <li>无法恢复任何数据</li>\n              </ul>\n            </div>\n          }\n          type=\"error\"\n          showIcon\n          style={{ marginBottom: 24 }}\n        />\n\n        <div style={{ marginBottom: 16 }}>\n          <Text strong>\n            请输入团队名称 \"<Text code>{teamDetail.name}</Text>\" 来确认删除：\n          </Text>\n        </div>\n\n        <Input\n          placeholder={`请输入：${teamDetail.name}`}\n          value={deleteConfirmText}\n          onChange={(e) => setDeleteConfirmText(e.target.value)}\n          style={{ marginBottom: 24 }}\n        />\n\n        <div style={{ textAlign: 'right' }}>\n          <Space>\n            <Button \n              onClick={() => {\n                setDeleteModalVisible(false);\n                setDeleteConfirmText('');\n              }}\n            >\n              取消\n            </Button>\n            <Button\n              type=\"primary\"\n              danger\n              loading={deleting}\n              disabled={deleteConfirmText !== teamDetail.name}\n              onClick={handleDeleteTeam}\n              icon={<DeleteOutlined />}\n            >\n              确认删除团队\n            </Button>\n          </Space>\n        </div>\n      </Modal>\n    </div>\n  );\n};\n\nexport default TeamSettings;\n", "/**\n * 集成团队管理页面\n * \n * 功能特性：\n * - 统一的团队管理界面，包含多个功能模块\n * - 选项卡布局，便于在不同管理功能之间切换\n * - 团队成员管理、账户管理、团队设置等功能\n * - 权限控制，只有团队创建者可以访问管理功能\n * \n * 模块组织：\n * - 团队成员管理：查看、添加、移除团队成员\n * - 成员账户管理：管理成员权限和角色\n * - 团队设置：编辑团队信息、删除团队\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { PageContainer } from '@ant-design/pro-components';\nimport { \n  Tabs, \n  Card, \n  Alert, \n  Spin, \n  Typography, \n  Space,\n  Tag,\n  Avatar,\n  Button\n} from 'antd';\nimport { \n  TeamOutlined, \n  UserOutlined, \n  SettingOutlined,\n  CrownOutlined,\n  InfoCircleOutlined\n} from '@ant-design/icons';\nimport { history } from '@umijs/max';\n\n// 导入子组件\nimport TeamMemberManagement from './components/TeamMemberManagement';\nimport TeamSettings from './components/TeamSettings';\n\n// 导入服务和类型\nimport { TeamService } from '@/services/team';\nimport type { TeamDetailResponse } from '@/types/api';\n\nconst { Title, Text } = Typography;\n\nconst TeamManagementPage: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n  const [teamDetail, setTeamDetail] = useState<TeamDetailResponse | null>(null);\n  const [activeTab, setActiveTab] = useState('members');\n\n  useEffect(() => {\n    fetchTeamDetail();\n  }, []);\n\n  const fetchTeamDetail = async () => {\n    try {\n      setLoading(true);\n      const detail = await TeamService.getCurrentTeamDetail();\n      setTeamDetail(detail);\n    } catch (error) {\n      console.error('获取团队详情失败:', error);\n      // 如果获取失败，可能是没有选择团队，跳转到团队选择页面\n      history.push('/user/team-select');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 权限检查：只有团队创建者可以访问管理功能\n  const hasManagePermission = teamDetail?.isCreator || false;\n\n  if (loading) {\n    return (\n      <PageContainer>\n        <div style={{ textAlign: 'center', padding: '50px 0' }}>\n          <Spin size=\"large\" />\n          <div style={{ marginTop: 16 }}>\n            <Text type=\"secondary\">正在加载团队信息...</Text>\n          </div>\n        </div>\n      </PageContainer>\n    );\n  }\n\n  if (!teamDetail) {\n    return (\n      <PageContainer>\n        <Card>\n          <div style={{ textAlign: 'center', padding: '50px 0' }}>\n            <InfoCircleOutlined style={{ fontSize: 48, color: '#faad14', marginBottom: 16 }} />\n            <Title level={4}>未找到团队信息</Title>\n            <Text type=\"secondary\">请先选择一个团队</Text>\n            <div style={{ marginTop: 16 }}>\n              <Button type=\"primary\" onClick={() => history.push('/user/team-select')}>\n                选择团队\n              </Button>\n            </div>\n          </div>\n        </Card>\n      </PageContainer>\n    );\n  }\n\n  // 权限不足提示\n  if (!hasManagePermission) {\n    return (\n      <PageContainer>\n        <Card>\n          <Alert\n            message=\"权限不足\"\n            description=\"只有团队创建者可以访问团队管理功能。如果您需要管理权限，请联系团队创建者。\"\n            type=\"warning\"\n            showIcon\n            action={\n              <Button size=\"small\" onClick={() => history.push('/dashboard')}>\n                返回首页\n              </Button>\n            }\n          />\n        </Card>\n      </PageContainer>\n    );\n  }\n\n  // 选项卡配置\n  const tabItems = [\n    {\n      key: 'members',\n      label: (\n        <Space>\n          <UserOutlined />\n          团队成员管理\n        </Space>\n      ),\n      children: (\n        <TeamMemberManagement\n          teamDetail={teamDetail}\n          onRefresh={fetchTeamDetail}\n        />\n      ),\n    },\n    {\n      key: 'settings',\n      label: (\n        <Space>\n          <SettingOutlined />\n          团队设置\n        </Space>\n      ),\n      children: (\n        <TeamSettings\n          teamDetail={teamDetail}\n          onRefresh={fetchTeamDetail}\n        />\n      ),\n    },\n  ];\n\n  return (\n    <PageContainer>\n      <Card>\n        <Tabs\n          activeKey={activeTab}\n          onChange={setActiveTab}\n          items={tabItems}\n          size=\"large\"\n          tabBarStyle={{ marginBottom: 24 }}\n        />\n      </Card>\n    </PageContainer>\n  );\n};\n\nexport default TeamManagementPage;\n"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;CAcC;;;;4BA+YD;;;eAAA;;;;;;wEA7Y2C;6BAmBpC;8BASA;6BAIqB;;;;;;;;;;AAG5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,gBAAU;AAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAO1B,MAAM,uBAA4D,CAAC,EACjE,UAAU,EACV,SAAS,EACV;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAuB,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAAC;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,eAAQ,EAAc,EAAE;IACtE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,WAAW,GAAG,UAAI,CAAC,OAAO;IAEjC,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,aAAa,MAAM,iBAAW,CAAC,qBAAqB;YAC1D,WAAW,cAAc,EAAE;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;YACd,WAAW,EAAE,GAAG,eAAe;QACjC,SAAU;YACR,WAAW;QACb;IACF;IAEA,QAAQ;IACR,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,YAAY,OAAO,MAAM,CAC5B,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,IACvB,MAAM,CAAC,CAAA,QAAS;YAEnB,MAAM,iBAAW,CAAC,aAAa,CAAC;gBAAE,QAAQ;YAAU;YACpD,aAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,UAAU,MAAM,CAAC,IAAI,CAAC;YAC9C,sBAAsB;YACtB,WAAW,WAAW;YACtB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,SAAS;IACT,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,iBAAW,CAAC,YAAY,CAAC,OAAO,EAAE;YACxC,aAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC;YACtC;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,SAAS;IACT,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,YAAY;YAClB,KAAK,MAAM,YAAY,UACrB,MAAM,iBAAW,CAAC,YAAY,CAAC;YAEjC,aAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,UAAU,MAAM,CAAC,IAAI,CAAC;YAC7C,mBAAmB,EAAE;YACrB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,OAAO;IACP,MAAM,kBAAkB,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,SAC7C,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACzD,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG5D,UAAU;IACV,MAAM,2BAA2B,OAAO,QAA4B;QAClE,IAAI;YACF,MAAM,iBAAW,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE;YAChD,aAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,OAAO,KAAK,GAAG,EAAE,OAAO,IAAI,CAAC,CAAC;YAC7D;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,aAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,QAAQ;IACR,MAAM,UAA2C;QAC/C;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,MAAc,uBACrB,2BAAC,WAAK;;sCACJ,2BAAC;4BAAK,MAAM;sCAAE;;;;;;wBACb,OAAO,SAAS,kBACf,2BAAC,SAAG;4BAAC,oBAAM,2BAAC,oBAAa;;;;;4BAAK,OAAM;sCAAO;;;;;;;;;;;;QAInD;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,sBACP,2BAAC;oBAAK,MAAK;8BAAa;;;;;;QAE5B;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,yBACP,2BAAC,SAAG;oBAAC,OAAO,WAAW,UAAU;8BAC9B,WAAW,OAAO;;;;;;QAGzB;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;QAC7D;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,OAAiB,IAAI,KAAK,MAAM,kBAAkB;QAC7D;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG;gBACV,IAAI,OAAO,SAAS,EAClB,qBAAO,2BAAC;oBAAK,MAAK;8BAAY;;;;;;gBAGhC,qBACE,2BAAC,WAAK;;wBACH,OAAO,QAAQ,iBACd,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAK;4BACL,SAAS,IAAM,yBAAyB,QAAQ;sCACjD;;;;;iDAID,2BAAC,YAAM;4BACL,MAAK;4BACL,MAAK;4BACL,SAAS,IAAM,yBAAyB,QAAQ;sCACjD;;;;;;sCAIH,2BAAC,gBAAU;4BACT,OAAM;4BACN,aAAa,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC;4BAChD,WAAW,IAAM,mBAAmB;4BACpC,QAAO;4BACP,YAAW;4BACX,QAAO;sCAEP,cAAA,2BAAC,YAAM;gCACL,MAAK;gCACL,MAAM;gCACN,MAAK;gCACL,oBAAM,2BAAC,qBAAc;;;;;0CACtB;;;;;;;;;;;;;;;;;YAMT;QACF;KACD;IAED,QAAQ;IACR,MAAM,eAAe;QACnB;QACA,UAAU;QACV,kBAAkB,CAAC,SAAgC,CAAA;gBACjD,UAAU,OAAO,SAAS;YAC5B,CAAA;IACF;IAEA,qBACE,2BAAC;;0BAEC,2BAAC,SAAG;gBAAC,QAAQ;gBAAI,OAAO;oBAAE,cAAc;gBAAG;;kCACzC,2BAAC,SAAG;wBAAC,MAAM;kCACT,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM;gCAC7B,sBAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;kCAI3B,2BAAC,SAAG;wBAAC,MAAM;kCACT,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;gCACrD,sBAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;kCAI3B,2BAAC,SAAG;wBAAC,MAAM;kCACT,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,QAAQ,EAAE,MAAM;gCACtD,sBAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;kCAI3B,2BAAC,SAAG;wBAAC,MAAM;kCACT,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,AAAC,CAAA,WAAW,EAAE,AAAD,EAAG,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;gCACtD,sBAAQ,2BAAC,oBAAa;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9B,2BAAC,UAAI;gBAAC,OAAO;oBAAE,cAAc;gBAAG;0BAC9B,cAAA,2BAAC,SAAG;oBAAC,SAAQ;oBAAgB,OAAM;;sCACjC,2BAAC,SAAG;sCACF,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,WAAK;wCACJ,aAAY;wCACZ,sBAAQ,2BAAC,qBAAc;;;;;wCACvB,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,OAAO;4CAAE,OAAO;wCAAI;wCACpB,UAAU;;;;;;oCAEX,gBAAgB,MAAM,GAAG,mBACxB,2BAAC,gBAAU;wCACT,OAAM;wCACN,aAAa,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,cAAc,CAAC;wCAC/D,WAAW;wCACX,QAAO;wCACP,YAAW;wCACX,QAAO;kDAEP,cAAA,2BAAC,YAAM;4CACL,MAAM;4CACN,oBAAM,2BAAC,qBAAc;;;;;;gDACtB;gDACQ,gBAAgB,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;;;;;sCAMxC,2BAAC,SAAG;sCACF,cAAA,2BAAC,YAAM;gCACL,MAAK;gCACL,oBAAM,2BAAC,sBAAe;;;;;gCACtB,SAAS,IAAM,sBAAsB;0CACtC;;;;;;;;;;;;;;;;;;;;;;0BAQP,2BAAC,UAAI;0BACH,cAAA,2BAAC,WAAK;oBACJ,SAAS;oBACT,YAAY;oBACZ,QAAO;oBACP,SAAS;oBACT,cAAc;oBACd,YAAY;wBACV,iBAAiB;wBACjB,iBAAiB;wBACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;wBACtC,UAAU;oBACZ;;;;;;;;;;;0BAKJ,2BAAC,WAAK;gBACJ,OAAM;gBACN,MAAM;gBACN,UAAU;oBACR,sBAAsB;oBACtB,WAAW,WAAW;gBACxB;gBACA,QAAQ;gBACR,OAAO;0BAEP,cAAA,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BACtC;4BACD,OAAM;sCAEN,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;;;;;;;;;;;sCAGhB,2BAAC,UAAI,CAAC,IAAI;sCACR,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCAAC,MAAK;wCAAU,UAAS;wCAAS,oBAAM,2BAAC,mBAAY;;;;;kDAAK;;;;;;kDAGjE,2BAAC,YAAM;wCAAC,SAAS,IAAM,sBAAsB;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE;GAhWM;;QASiB,UAAI,CAAC;;;KATtB;IAkWN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7Zf;;;;;;;;;;;;CAYC;;;;4BAgVD;;;eAAA;;;;;;wEA9UgC;6BAgBzB;8BAUA;4BACiB;6BAGI;;;;;;;;;;AAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,gBAAU;AAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,WAAK;AAO1B,MAAM,eAA4C,CAAC,EACjD,UAAU,EACV,SAAS,EACV;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,IAAA,eAAQ,EAAC;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,eAAQ,EAAC;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,eAAQ,EAAC;IACzC,MAAM,CAAC,KAAK,GAAG,UAAI,CAAC,OAAO;IAE3B,SAAS;IACT,MAAM,aAAa;QACjB,KAAK,cAAc,CAAC;YAClB,MAAM,WAAW,IAAI;YACrB,aAAa,WAAW,WAAW,IAAI;QACzC;QACA,YAAY;IACd;IAEA,OAAO;IACP,MAAM,mBAAmB;QACvB,YAAY;QACZ,KAAK,WAAW;IAClB;IAEA,SAAS;IACT,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,YAAY;YACZ,MAAM,iBAAW,CAAC,iBAAiB,CAAC;YACpC,aAAO,CAAC,OAAO,CAAC;YAChB,YAAY;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA,OAAO;IACP,MAAM,mBAAmB;QACvB,IAAI,sBAAsB,WAAW,IAAI,EAAE;YACzC,aAAO,CAAC,KAAK,CAAC;YACd;QACF;QAEA,IAAI;YACF,YAAY;YACZ,MAAM,iBAAW,CAAC,iBAAiB;YACnC,aAAO,CAAC,OAAO,CAAC;YAChB,sBAAsB;YACtB,iBAAiB;YACjB,YAAO,CAAC,IAAI,CAAC;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,aAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,YAAY;QACd;IACF;IAEA,qBACE,2BAAC;;0BAEC,2BAAC,SAAG;gBAAC,QAAQ;gBAAI,OAAO;oBAAE,cAAc;gBAAG;;kCACzC,2BAAC,SAAG;wBAAC,MAAM;kCACT,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,EAAE;gCACpB,sBAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;kCAI3B,2BAAC,SAAG;wBAAC,MAAM;kCACT,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,WAAW,WAAW;gCAC7B,sBAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;kCAI3B,2BAAC,SAAG;wBAAC,MAAM;kCACT,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,IAAI,KAAK,WAAW,SAAS,EAAE,kBAAkB;gCACxD,sBAAQ,2BAAC,uBAAgB;;;;;;;;;;;;;;;;;;;;kCAI/B,2BAAC,SAAG;wBAAC,MAAM;kCACT,cAAA,2BAAC,UAAI;sCACH,cAAA,2BAAC,eAAS;gCACR,OAAM;gCACN,OAAO,IAAI,KAAK,WAAW,SAAS,EAAE,kBAAkB;gCACxD,sBAAQ,2BAAC,mBAAY;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7B,2BAAC,UAAI;gBACH,OAAM;gBACN,OACE,CAAC,0BACC,2BAAC,YAAM;oBACL,MAAK;oBACL,oBAAM,2BAAC,mBAAY;;;;;oBACnB,SAAS;8BACV;;;;;;gBAKL,OAAO;oBAAE,cAAc;gBAAG;0BAEzB,yBACC,2BAAC,UAAI;oBACH,MAAM;oBACN,QAAO;oBACP,UAAU;;sCAEV,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAU;gCACrC;oCAAE,KAAK;oCAAG,KAAK;oCAAI,SAAS;gCAAoB;6BACjD;sCAED,cAAA,2BAAC,WAAK;gCAAC,aAAY;;;;;;;;;;;sCAErB,2BAAC,UAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCACL;oCAAE,KAAK;oCAAK,SAAS;gCAAiB;6BACvC;sCAED,cAAA,2BAAC;gCACC,MAAM;gCACN,aAAY;gCACZ,SAAS;gCACT,WAAW;;;;;;;;;;;sCAGf,2BAAC,UAAI,CAAC,IAAI;sCACR,cAAA,2BAAC,WAAK;;kDACJ,2BAAC,YAAM;wCACL,MAAK;wCACL,UAAS;wCACT,SAAS;wCACT,oBAAM,2BAAC,mBAAY;;;;;kDACpB;;;;;;kDAGD,2BAAC,YAAM;wCAAC,SAAS;kDAAkB;;;;;;;;;;;;;;;;;;;;;;yCAOzC,2BAAC;;sCACC,2BAAC;4BAAI,OAAO;gCAAE,cAAc;4BAAG;;8CAC7B,2BAAC;oCAAK,MAAM;8CAAC;;;;;;8CACb,2BAAC;oCAAM,OAAO;oCAAG,OAAO;wCAAE,QAAQ;oCAAQ;8CAAI,WAAW,IAAI;;;;;;;;;;;;sCAE/D,2BAAC;;8CACC,2BAAC;oCAAK,MAAM;8CAAC;;;;;;8CACb,2BAAC;oCAAU,OAAO;wCAAE,WAAW;oCAAE;8CAC9B,WAAW,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;0BAQrC,2BAAC,UAAI;gBACH,qBACE,2BAAC,WAAK;;sCACJ,2BAAC,sBAAe;4BAAC,OAAO;gCAAE,OAAO;4BAAU;;;;;;sCAC3C,2BAAC;4BAAK,MAAK;sCAAS;;;;;;;;;;;;;kCAIxB,2BAAC,WAAK;wBACJ,SAAQ;wBACR,aAAY;wBACZ,MAAK;wBACL,QAAQ;wBACR,OAAO;4BAAE,cAAc;wBAAG;;;;;;kCAG5B,2BAAC,gBAAU;wBACT,OAAM;wBACN,aAAY;wBACZ,WAAW,IAAM,sBAAsB;wBACvC,QAAO;wBACP,YAAW;wBACX,QAAO;kCAEP,cAAA,2BAAC,YAAM;4BACL,MAAM;4BACN,oBAAM,2BAAC,qBAAc;;;;;4BACrB,MAAK;sCACN;;;;;;;;;;;;;;;;;0BAOL,2BAAC,WAAK;gBACJ,qBACE,2BAAC,WAAK;;sCACJ,2BAAC,gCAAyB;4BAAC,OAAO;gCAAE,OAAO;4BAAU;;;;;;sCACrD,2BAAC;4BAAK,MAAK;sCAAS;;;;;;;;;;;;gBAGxB,MAAM;gBACN,UAAU;oBACR,sBAAsB;oBACtB,qBAAqB;gBACvB;gBACA,QAAQ;gBACR,OAAO;;kCAEP,2BAAC,WAAK;wBACJ,SAAQ;wBACR,2BACE,2BAAC;;8CACC,2BAAC;8CAAE;;;;;;8CACH,2BAAC;;sDACC,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;sDACJ,2BAAC;sDAAG;;;;;;;;;;;;;;;;;;wBAIV,MAAK;wBACL,QAAQ;wBACR,OAAO;4BAAE,cAAc;wBAAG;;;;;;kCAG5B,2BAAC;wBAAI,OAAO;4BAAE,cAAc;wBAAG;kCAC7B,cAAA,2BAAC;4BAAK,MAAM;;gCAAC;8CACF,2BAAC;oCAAK,IAAI;8CAAE,WAAW,IAAI;;;;;;gCAAQ;;;;;;;;;;;;kCAIhD,2BAAC,WAAK;wBACJ,aAAa,CAAC,IAAI,EAAE,WAAW,IAAI,CAAC,CAAC;wBACrC,OAAO;wBACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;wBACpD,OAAO;4BAAE,cAAc;wBAAG;;;;;;kCAG5B,2BAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAQ;kCAC/B,cAAA,2BAAC,WAAK;;8CACJ,2BAAC,YAAM;oCACL,SAAS;wCACP,sBAAsB;wCACtB,qBAAqB;oCACvB;8CACD;;;;;;8CAGD,2BAAC,YAAM;oCACL,MAAK;oCACL,MAAM;oCACN,SAAS;oCACT,UAAU,sBAAsB,WAAW,IAAI;oCAC/C,SAAS;oCACT,oBAAM,2BAAC,qBAAc;;;;;8CACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAnSM;;QASW,UAAI,CAAC;;;KAThB;IAqSN,WAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5Vf;;;;;;;;;;;;;CAaC;;;;4BAkKD;;;eAAA;;;;;;;wEAhK2C;sCACb;6BAWvB;8BAOA;4BACiB;sFAGS;8EACR;6BAGG;;;;;;;;;;AAG5B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,gBAAU;AAElC,MAAM,qBAA+B;;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,eAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,eAAQ,EAA4B;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,eAAQ,EAAC;IAE3C,IAAA,gBAAS,EAAC;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,MAAM,iBAAW,CAAC,oBAAoB;YACrD,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,6BAA6B;YAC7B,YAAO,CAAC,IAAI,CAAC;QACf,SAAU;YACR,WAAW;QACb;IACF;IAEA,uBAAuB;IACvB,MAAM,sBAAsB,CAAA,uBAAA,iCAAA,WAAY,SAAS,KAAI;IAErD,IAAI,SACF,qBACE,2BAAC,4BAAa;kBACZ,cAAA,2BAAC;YAAI,OAAO;gBAAE,WAAW;gBAAU,SAAS;YAAS;;8BACnD,2BAAC,UAAI;oBAAC,MAAK;;;;;;8BACX,2BAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAG;8BAC1B,cAAA,2BAAC;wBAAK,MAAK;kCAAY;;;;;;;;;;;;;;;;;;;;;;IAOjC,IAAI,CAAC,YACH,qBACE,2BAAC,4BAAa;kBACZ,cAAA,2BAAC,UAAI;sBACH,cAAA,2BAAC;gBAAI,OAAO;oBAAE,WAAW;oBAAU,SAAS;gBAAS;;kCACnD,2BAAC,yBAAkB;wBAAC,OAAO;4BAAE,UAAU;4BAAI,OAAO;4BAAW,cAAc;wBAAG;;;;;;kCAC9E,2BAAC;wBAAM,OAAO;kCAAG;;;;;;kCACjB,2BAAC;wBAAK,MAAK;kCAAY;;;;;;kCACvB,2BAAC;wBAAI,OAAO;4BAAE,WAAW;wBAAG;kCAC1B,cAAA,2BAAC,YAAM;4BAAC,MAAK;4BAAU,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;sCAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUrF,SAAS;IACT,IAAI,CAAC,qBACH,qBACE,2BAAC,4BAAa;kBACZ,cAAA,2BAAC,UAAI;sBACH,cAAA,2BAAC,WAAK;gBACJ,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,QAAQ;gBACR,sBACE,2BAAC,YAAM;oBAAC,MAAK;oBAAQ,SAAS,IAAM,YAAO,CAAC,IAAI,CAAC;8BAAe;;;;;;;;;;;;;;;;;;;;;IAU5E,QAAQ;IACR,MAAM,WAAW;QACf;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,mBAAY;;;;;oBAAG;;;;;;;YAIpB,wBACE,2BAAC,6BAAoB;gBACnB,YAAY;gBACZ,WAAW;;;;;;QAGjB;QACA;YACE,KAAK;YACL,qBACE,2BAAC,WAAK;;kCACJ,2BAAC,sBAAe;;;;;oBAAG;;;;;;;YAIvB,wBACE,2BAAC,qBAAY;gBACX,YAAY;gBACZ,WAAW;;;;;;QAGjB;KACD;IAED,qBACE,2BAAC,4BAAa;kBACZ,cAAA,2BAAC,UAAI;sBACH,cAAA,2BAAC,UAAI;gBACH,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,MAAK;gBACL,aAAa;oBAAE,cAAc;gBAAG;;;;;;;;;;;;;;;;AAK1C;GA9HM;KAAA;IAgIN,WAAe"}